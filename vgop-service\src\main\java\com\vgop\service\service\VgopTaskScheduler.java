package com.vgop.service.service;

import com.vgop.service.config.TaskConfig;
import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.config.VgopProperties;
import com.vgop.service.dto.FileTransferResponse;
import com.vgop.service.dto.TaskExecutionRequest;
import com.vgop.service.dto.TaskExecutionResponse;
import com.vgop.service.entity.TaskConfig.TaskStatus;
import com.vgop.service.exception.TaskExecutionException;
import com.vgop.service.sftp.SftpService;
import com.vgop.service.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.HashSet;
import java.util.Set;

/**
 * VGOP任务调度服务
 */
@Slf4j
@Service("vgopTaskExecutor")
@RequiredArgsConstructor
public class VgopTaskScheduler {

    private final DataExportService dataExportService;
    private final FileProcessingService fileProcessingService;
    private final RevisionService revisionService;
    private final VgopAppConfig vgopAppConfig;
    private final VgopProperties vgopProperties;
    private final DataValidationService dataValidationService;
    private final SftpService sftpService;
    private final DataQualityService dataQualityService;
    private final FileListNotificationService fileListNotificationService;

    /**
     * 根据接口ID执行单个统计任务
     */
    @Async("taskExecutor")
    public CompletableFuture<TaskExecutionResponse> executeTask(TaskExecutionRequest request) {
        String interfaceId = request.getInterfaceId();
        log.info("开始执行统计任务: {}, 接口ID: {}", request.getActionInstanceId(), interfaceId);

        LocalDateTime startTime = LocalDateTime.now();
        TaskExecutionResponse.TaskExecutionResponseBuilder responseBuilder = TaskExecutionResponse.builder()
                .taskId(request.getActionInstanceId())
                .status(TaskStatus.RUNNING)
                .startTime(startTime)
                .success(false);

        try {
            // 1. 根据interfaceId查找任务配置
            TaskConfig taskConfig = vgopAppConfig.getDailyTask(interfaceId);
            if (taskConfig == null) {
                taskConfig = vgopAppConfig.getMonthlyTask(interfaceId);
            }
            if (taskConfig == null) {
                throw new TaskExecutionException("未找到接口ID为 '" + interfaceId + "' 的任务配置");
            }

            // 2. 执行数据导出（对于VGOP1-R2.11-24101任务，此步骤内部会先通过dbaccess调用存储过程）
            log.info("开始执行数据导出任务: {}", interfaceId);
            if ("VGOP1-R2.11-24101".equals(interfaceId)) {
                log.info("VGOP业务分析任务执行顺序 - 步骤1: 即将通过dbaccess调用存储过程 bmssp_VGOP_banalyse");
            }
            
            DataExportService.ExportResult exportResult = dataExportService.exportData(taskConfig, request.getDateId(), 1);
            if (!exportResult.isSuccess()) {
                throw new TaskExecutionException("数据导出失败: " + exportResult.getErrorMessage());
            }
            
            if ("VGOP1-R2.11-24101".equals(interfaceId)) {
                log.info("VGOP业务分析任务执行顺序 - 步骤2: dbaccess存储过程执行完成，数据导出成功");
            }

            // 3. 定位导出的unl文件
            String exportRoot = vgopProperties.getExportPath();
            String cycleType = taskConfig.getTaskType();
            
            // 统一日期处理逻辑：对于日统计任务，unl文件导出目录也应使用前一天日期（与.dat文件目录保持一致）
            String exportDirDate = request.getDateId();
            if (taskConfig.getTaskType().equals("daily")) {
                exportDirDate = DateTimeUtil.calculateBeforeDay(request.getDateId());
                log.debug("日统计任务unl文件导出目录使用前一天日期: {} -> {}", request.getDateId(), exportDirDate);
            }
            
            String exportPath = String.format("%s/%s/%s/", exportRoot, exportDirDate, cycleType);
            
            String tempFileName = taskConfig.getExport().getTempFileNameTemplate()
                    .replace("{dataDate}", request.getDateId())
                    .replace("{interfaceId}", interfaceId);
            
             if (taskConfig.getTaskType().equals("daily")) {
                String beforeDay = DateTimeUtil.calculateBeforeDay(request.getDateId());
                tempFileName = tempFileName.replace("{previousDay}", beforeDay);
            }

            String unloadFilePath = exportPath + tempFileName;
            
            if (!new File(unloadFilePath).exists()) {
                 throw new TaskExecutionException("导出的临时文件不存在: " + unloadFilePath);
            }

            // 4. 对导出的unl文件进行校验和清理
            String cleanedFilePath = dataValidationService.validateAndCleanFile(unloadFilePath, interfaceId, request.getDateId(), taskConfig.getTaskType());

            // 5. 处理生成的文件（分割、重命名等），使用清理后的文件
            FileProcessResult fileProcessResult = processFile(cleanedFilePath, request, taskConfig);
            
            // 5.5. 数据质量检测（不合规数据占比校验、数据量波动检测、Excel报告生成）
            // 修复日期参数一致性问题：对于日统计任务，数据质量检测也应使用前一天日期
            String qualityCheckDataDate = request.getDateId();
            if (taskConfig.getTaskType().equals("daily")) {
                qualityCheckDataDate = DateTimeUtil.calculateBeforeDay(request.getDateId());
                log.debug("日统计任务数据质量检测使用前一天日期: {} -> {}", request.getDateId(), qualityCheckDataDate);
            }
            
            DataQualityService.QualityCheckResult qualityResult = dataQualityService.performQualityCheck(
                    cleanedFilePath, unloadFilePath, interfaceId, qualityCheckDataDate, fileProcessResult.getOutputDir(), request.getActionInstanceId());
            
            // 记录质量检测结果到响应中
            if (qualityResult.isNonComplianceThresholdExceeded() || qualityResult.isVolumeFluctuationDetected()) {
                log.warn("数据质量检测发现异常 - 接口ID: {}, 不合规率: %.2f%%, 波动率: %.2f%%, 告警数量: {}", 
                        interfaceId, qualityResult.getNonComplianceRate() * 100, 
                        qualityResult.getVolumeFluctuationRate() * 100, qualityResult.getAlerts().size());
            }
            
            // 统一处理日期转换：对于日统计任务，文件操作需要使用前一天日期
            String fileOperationDateId = request.getDateId();
            if (taskConfig.getTaskType().equals("daily")) {
                fileOperationDateId = DateTimeUtil.calculateBeforeDay(request.getDateId());
                log.debug("日统计任务文件操作使用前一天日期: {} -> {}", request.getDateId(), fileOperationDateId);
            }
            
            // 6. SFTP传输生成的.dat和.verf文件（精确传输当前任务的文件）
            SftpService.UploadResult uploadResult = sftpService.uploadVgopDataFilesForInterface(
                    fileOperationDateId, interfaceId);
            
            if (!uploadResult.isSuccess()) {
                throw new TaskExecutionException("SFTP传输失败");
            }
            
            // 7. 向DMZ发送文件清单通知（实现文件中转转发机制）
            FileTransferResponse dmzResponse = null;
            try {
                log.info("开始向DMZ发送文件清单通知 - 任务ID: {}, 接口ID: {}", request.getActionInstanceId(), interfaceId);
                
                dmzResponse = fileListNotificationService.notifyDmzFileTransfer(
                        request.getActionInstanceId(), 
                        fileOperationDateId, 
                        interfaceId, 
                        taskConfig.getTaskType(), 
                        fileProcessResult.getRevision(),  // 使用processFile返回的revision
                        fileProcessResult.getOutputDir());
                log.info("dmzResponse is {}", dmzResponse);
                
                // 改进的响应处理逻辑：支持降级判断和详细状态分析
                if (dmzResponse != null) {
                    // 首先检查标准的status字段
                    if (dmzResponse.getStatus() != null) {
                        switch (dmzResponse.getStatus()) {
                            case "SUCCESS":
                                log.info("DMZ文件中转通知成功 - 任务ID: {}, 成功文件: {}, 失败文件: {}", 
                                         request.getActionInstanceId(), dmzResponse.getSuccessFiles(), dmzResponse.getFailedFiles());
                                break;
                            case "PARTIAL":
                                log.warn("DMZ文件中转部分成功 - 任务ID: {}, 成功文件: {}, 失败文件: {}", 
                                         request.getActionInstanceId(), dmzResponse.getSuccessFiles(), dmzResponse.getFailedFiles());
                                break;
                            case "FAILED":
                                log.error("DMZ文件中转完全失败 - 任务ID: {}, 错误: {}", 
                                          request.getActionInstanceId(), dmzResponse.getMessage());
                                break;
                            default:
                                log.error("DMZ文件中转返回未知状态 - 任务ID: {}, 状态: {}, 错误: {}", 
                                          request.getActionInstanceId(), dmzResponse.getStatus(), dmzResponse.getMessage());
                                break;
                        }
                    } else {
                        // 降级处理：当status为null时，通过message字段判断成功状态
                        String message = dmzResponse.getMessage();
                        if (message != null && (message.contains("处理完成") || message.contains("成功") || 
                                              message.contains("文件中转处理完成") || message.contains("传输完成"))) {
                            log.info("DMZ文件中转通知成功（降级判断） - 任务ID: {}, 消息: {}", 
                                     request.getActionInstanceId(), message);
                        } else {
                            log.warn("DMZ文件中转状态字段为null，且消息无法判断成功状态 - 任务ID: {}, 消息: {}", 
                                     request.getActionInstanceId(), message);
                        }
                    }
                } else {
                    log.error("DMZ文件中转响应为空 - 任务ID: {}", request.getActionInstanceId());
                }
                
            } catch (Exception e) {
                log.warn("DMZ文件清单通知失败，但不影响主任务 - 任务ID: {}, 错误: {}", request.getActionInstanceId(), e.getMessage());
                // 注意：DMZ通知失败不应该影响主任务的成功状态，因为内网到DMZ的SFTP传输已经成功
            }
            
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();

            // 构建包含数据质量检测结果和DMZ中转结果的消息
            StringBuilder messageBuilder = new StringBuilder();
            messageBuilder.append(String.format("任务 %s 执行成功 - 上传.dat文件: %d个, .verf文件: %d个", 
                    interfaceId, uploadResult.getDatFileCount(), uploadResult.getVerfFileCount()));
            
            // 添加数据质量检测结果
            messageBuilder.append(String.format(" | 数据质量: 总记录数=%d, 错误记录数=%d, 不合规率=%.2f%%", 
                    qualityResult.getTotalRecords(), qualityResult.getErrorRecords(), 
                    qualityResult.getNonComplianceRate() * 100));
            
            if (qualityResult.getVolumeFluctuationRate() > 0) {
                messageBuilder.append(String.format(", 数据量波动=%.2f%%", qualityResult.getVolumeFluctuationRate() * 100));
            }
            
            if (qualityResult.getReportFilePath() != null) {
                messageBuilder.append(" | 已生成不合规数据报告");
            }
            
            if (!qualityResult.getAlerts().isEmpty()) {
                messageBuilder.append(" | 质量告警: ").append(qualityResult.getAlerts().size()).append("个");
            }
            
            // 添加DMZ中转结果
            if (dmzResponse != null) {
                // 支持降级判断的DMZ状态处理
                String dmzStatus = dmzResponse.getStatus();
                Integer successCount = dmzResponse.getSuccessFiles();
                Integer failedCount = dmzResponse.getFailedFiles();
                
                // 如果status为null，通过message进行降级判断
                if (dmzStatus == null) {
                    String message = dmzResponse.getMessage();
                    if (message != null && (message.contains("处理完成") || message.contains("成功") || 
                                          message.contains("文件中转处理完成") || message.contains("传输完成"))) {
                        dmzStatus = "SUCCESS";
                        // 如果统计数据为null，设置默认值
                        if (successCount == null) successCount = 0;
                        if (failedCount == null) failedCount = 0;
                    } else {
                        dmzStatus = "UNKNOWN";
                        if (successCount == null) successCount = 0;
                        if (failedCount == null) failedCount = 0;
                    }
                }
                
                messageBuilder.append(String.format(" | DMZ中转: %s, 成功: %d个, 失败: %d个", 
                        dmzStatus, 
                        successCount != null ? successCount : 0, 
                        failedCount != null ? failedCount : 0));
                        
                // 如果是降级判断的成功状态，添加说明
                if ("SUCCESS".equals(dmzStatus) && dmzResponse.getStatus() == null) {
                    messageBuilder.append(" (降级判断)");
                }
            } else {
                messageBuilder.append(" | DMZ中转: 未配置或失败");
            }

            responseBuilder.status(TaskStatus.SUCCESS)
                    .endTime(endTime)
                    .executionTimeMs(executionTime)
                    .generatedFiles(fileProcessResult.getOutputDir() != null ? Collections.singletonList(fileProcessResult.getOutputDir()) : Collections.emptyList())
                    .success(true)
                    .message(messageBuilder.toString());

            log.info("统计任务执行完成: {}, 接口ID: {}", request.getActionInstanceId(), interfaceId);

        } catch (Exception e) {
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();

            responseBuilder.status(TaskStatus.FAILED)
                    .endTime(endTime)
                    .executionTimeMs(executionTime)
                    .errorMessage(e.getMessage())
                    .message("任务 " + interfaceId + " 执行失败 - " + e.getMessage());

            log.error("统计任务 {} 执行失败, 接口ID: {}, 错误: {}", request.getActionInstanceId(), interfaceId, e.getMessage(), e);
        }

        return CompletableFuture.completedFuture(responseBuilder.build());
    }

    /**
     * 处理文件（通用版本）
     */
    private FileProcessResult processFile(String unloadFilePath, TaskExecutionRequest request, TaskConfig taskConfig) {
        if (unloadFilePath == null) {
            return null;
        }

        String dataDate = request.getDateId();
        String cycleType = taskConfig.getTaskType().equals("daily") ? "day" : "month";
        
        // 对于日统计任务，输出目录应使用前一天日期（与Shell脚本逻辑保持一致）
        String outputDirDate = dataDate;
        if (taskConfig.getTaskType().equals("daily")) {
            outputDirDate = DateTimeUtil.calculateBeforeDay(dataDate);
            log.debug("日统计任务使用前一天日期作为输出目录: {} -> {}", dataDate, outputDirDate);
        }
        
        String outputDir = fileProcessingService.generateOutputDirectory(
                request.getImagePath(), outputDirDate, cycleType);
        fileProcessingService.ensureDirectoryExists(outputDir);
        
        // 对于日统计任务，文件名中的日期也应使用前一天日期（与Shell脚本逻辑保持一致）
        String fileNameDate = dataDate;
        if (taskConfig.getTaskType().equals("daily")) {
            fileNameDate = outputDirDate; // 使用与输出目录相同的前一天日期
            log.debug("日统计任务文件名使用前一天日期: {} -> {}", dataDate, fileNameDate);
        }
        
        // 使用文件名模板生成不带后缀的文件名
        String baseFileName = taskConfig.getExport().getOutputFileNameTemplate()
            .replace("{dataDate}", fileNameDate)
            .replace("_{revTimes}", "")
            .replace("_{fileNum}", "")
            .replace(".dat", "");

        String revision = revisionService.getNextRevision(fileNameDate, baseFileName);

        // 文件处理服务需要知道文件名前缀和脚本ID来进行分割和命名
        // 我们从文件名模板中提取这些信息
        String[] nameParts = baseFileName.split("_");
        String filePrefix = nameParts[0] + "_" + nameParts[1]; // e.g., "a_10000" or "i_10000"
        String scriptIdentifier = taskConfig.getInterfaceName(); // e.g., "VGOP1-R2.10-24201"

        fileProcessingService.splitAndProcessFile(unloadFilePath, outputDir,
                filePrefix, scriptIdentifier, fileNameDate, revision);

        return new FileProcessResult(outputDir, revision);
    }

    /**
     * 通过SFTP传输生成的.dat和.verf文件（优化版本 - 支持任务粒度精确传输）
     */
    private void transferFiles(String outputDir, String dataDate) {
        transferFiles(outputDir, dataDate, null, null);
    }

    /**
     * 通过SFTP传输生成的.dat和.verf文件（重载版本 - 支持任务范围过滤）
     * 
     * @param outputDir 输出目录
     * @param dataDate 数据日期
     * @param interfaceId 接口ID（用于单任务场景的文件过滤）
     * @param taskContext 任务上下文（用于批量任务场景的文件过滤）
     */
    private void transferFiles(String outputDir, String dataDate, String interfaceId, TaskTransferContext taskContext) {
        if (outputDir == null) {
            log.warn("输出目录为空，跳过SFTP传输");
            return;
        }

        try {
            log.info("开始SFTP传输文件，输出目录: {}, 数据日期: {}, 接口ID: {}", 
                     outputDir, dataDate, interfaceId != null ? interfaceId : "批量任务");
            
            // 构建远程目录路径
            String remoteDir = vgopAppConfig.getSftp().getRemoteBasePath();
            if (!remoteDir.endsWith("/")) {
                remoteDir += "/";
            }
            remoteDir += dataDate;
            
            File outputDirectory = new File(outputDir);
            if (!outputDirectory.exists() || !outputDirectory.isDirectory()) {
                throw new TaskExecutionException("输出目录不存在或不是目录: " + outputDir);
            }

            // 获取需要传输的文件（根据任务范围过滤）
            File[] files = getFilesToTransfer(outputDirectory, interfaceId, taskContext);
            
            if (files == null || files.length == 0) {
                log.warn("输出目录中没有找到符合条件的.dat或.verf文件: {}", outputDir);
                return;
            }

            int datFileCount = 0;
            int verfFileCount = 0;
            int failedCount = 0;

            // 遍历并上传筛选后的文件
            for (File file : files) {
                try {
                    String localFilePath = file.getAbsolutePath();
                    String remoteFilePath = remoteDir + "/" + file.getName();
                    
                    sftpService.uploadFile(localFilePath, remoteFilePath);
                    
                    if (file.getName().endsWith(".dat")) {
                        datFileCount++;
                        log.debug("成功上传.dat文件: {}", file.getName());
                    } else if (file.getName().endsWith(".verf")) {
                        verfFileCount++;
                        log.debug("成功上传.verf文件: {}", file.getName());
                    }
                    
                } catch (Exception e) {
                    failedCount++;
                    log.error("上传文件失败: {}", file.getName(), e);
                }
            }

            log.info("SFTP传输完成 - .dat文件: {}个, .verf文件: {}个, 失败: {}个", 
                     datFileCount, verfFileCount, failedCount);

            if (failedCount > 0) {
                throw new TaskExecutionException(
                    String.format("SFTP传输部分失败 - 成功: %d个, 失败: %d个", 
                                 datFileCount + verfFileCount, failedCount));
            }

        } catch (Exception e) {
            log.error("SFTP传输过程中发生错误: {}", e.getMessage(), e);
            throw new TaskExecutionException("SFTP传输失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据任务范围过滤需要传输的文件
     * 
     * @param outputDirectory 输出目录
     * @param interfaceId 接口ID（单任务场景）
     * @param taskContext 任务上下文（批量任务场景）
     * @return 需要传输的文件数组
     */
    private File[] getFilesToTransfer(File outputDirectory, String interfaceId, TaskTransferContext taskContext) {
        if (interfaceId != null) {
            // 单任务场景：只传输当前任务生成的文件
            return outputDirectory.listFiles((dir, name) -> {
                if (!name.endsWith(".dat") && !name.endsWith(".verf")) {
                    return false;
                }
                
                // 检查文件名是否包含当前任务的接口ID
                // 文件名格式：{prefix}_{dataDate}_{interfaceId}_{revision}_{fileNum}.dat/.verf
                String[] nameParts = name.split("_");
                if (nameParts.length >= 4) {
                    String fileInterfaceId = nameParts[3];
                    // 处理.verf文件（没有文件序号部分）
                    if (name.endsWith(".verf") && nameParts.length >= 3) {
                        fileInterfaceId = nameParts[3].replace(".verf", "");
                    }
                    // 处理.dat文件（有文件序号部分）
                    else if (name.endsWith(".dat") && nameParts.length >= 5) {
                        fileInterfaceId = nameParts[3];
                    }
                    
                    return interfaceId.equals(fileInterfaceId);
                }
                return false;
            });
        } else if (taskContext != null) {
            // 批量任务场景：传输当前批次中所有任务生成的文件
            return outputDirectory.listFiles((dir, name) -> {
                if (!name.endsWith(".dat") && !name.endsWith(".verf")) {
                    return false;
                }
                
                // 检查文件是否属于当前批次的任务
                return taskContext.shouldTransferFile(name);
            });
        } else {
            // 兼容模式：传输所有.dat和.verf文件
            log.warn("未指定任务范围，将传输所有.dat和.verf文件");
            return outputDirectory.listFiles((dir, name) -> 
                name.endsWith(".dat") || name.endsWith(".verf"));
        }
    }

    /**
     * 任务传输上下文
     * 用于批量任务场景的文件范围控制
     */
    public static class TaskTransferContext {
        private final Set<String> interfaceIds;
        private final String dataDate;
        private final String batchId;
        
        public TaskTransferContext(Set<String> interfaceIds, String dataDate, String batchId) {
            this.interfaceIds = interfaceIds != null ? interfaceIds : new HashSet<>();
            this.dataDate = dataDate;
            this.batchId = batchId;
        }
        
        /**
         * 判断文件是否应该被传输
         * 
         * @param fileName 文件名
         * @return true 如果文件应该被传输
         */
        public boolean shouldTransferFile(String fileName) {
            if (interfaceIds.isEmpty()) {
                return true; // 如果没有指定接口ID，传输所有文件
            }
            
            // 解析文件名中的接口ID
            String[] nameParts = fileName.split("_");
            if (nameParts.length >= 4) {
                String fileInterfaceId = nameParts[3];
                // 处理.verf文件
                if (fileName.endsWith(".verf")) {
                    fileInterfaceId = fileInterfaceId.replace(".verf", "");
                }
                
                return interfaceIds.contains(fileInterfaceId);
            }
            
            return false;
        }
        
        public Set<String> getInterfaceIds() {
            return new HashSet<>(interfaceIds);
        }
        
        public String getDataDate() {
            return dataDate;
        }
        
        public String getBatchId() {
            return batchId;
        }
    }

    /**
     * 文件处理结果
     */
    private static class FileProcessResult {
        private final String outputDir;
        private final String revision;

        public FileProcessResult(String outputDir, String revision) {
            this.outputDir = outputDir;
            this.revision = revision;
        }

        public String getOutputDir() {
            return outputDir;
        }

        public String getRevision() {
            return revision;
        }
    }
} 