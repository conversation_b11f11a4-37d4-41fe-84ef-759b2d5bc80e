package com.vgop.service.service;

import com.vgop.service.config.TaskConfig;
import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.config.VgopProperties;
import com.vgop.service.dao.DataExportMapper;
import com.vgop.service.exception.DatabaseException;
import com.vgop.service.util.DatabaseUtil;
import com.vgop.service.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import com.vgop.service.util.MdcUtil;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 数据导出服务
 * 负责各种业务数据的查询和导出，对应Shell脚本中的SQL查询和unload操作
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataExportService {
    
    private final VgopProperties vgopProperties;
    private final UnloadExecutorService unloadExecutorService;
    private final DatabaseUtil databaseUtil;
    
    /**
     * 根据任务配置导出数据
     * 
     * @param taskConfig 任务配置
     * @param dataDate 数据日期
     * @param revision 版本号
     * @return 导出结果
     */
    public ExportResult exportData(TaskConfig taskConfig, String dataDate, int revision) {
        String interfaceId = taskConfig.getInterfaceId();
        
        // 设置MDC上下文，确保该线程后续的所有日志都包含interfaceId信息
        MdcUtil.setInterfaceContext(interfaceId, dataDate, revision);
        MdcUtil.setOperationType("export");
        
        try {
            log.info("开始导出接口数据: interfaceId={}, dataDate={}, revision={}", interfaceId, dataDate, revision);
            // 获取导出配置
            TaskConfig.ExportConfig exportConfig = taskConfig.getExport();
            if (exportConfig == null) {
                return new ExportResult(interfaceId, false, "导出配置为空");
            }
            
            if (exportConfig.getSqlTemplate() == null) {
                return new ExportResult(interfaceId, false, "SQL模板为空");
            }
            
            if (exportConfig.getTempFileNameTemplate() == null) {
                return new ExportResult(interfaceId, false, "临时文件名模板为空");
            }
            
            // **特殊处理：VGOP1-R2.11-24101业务分析任务需要先调用存储过程**
            if ("VGOP1-R2.11-24101".equals(interfaceId)) {
                log.info("检测到VGOP业务分析任务，执行存储过程调用: {}", interfaceId);
                
                try {
                    // 调用业务分析存储过程：bmssp_VGOP_banalyse
                    // 根据原始脚本VGOP1-R2.11-24101.sh，参数为：debugFile, traceFlag, taskId
                    String debugFile = ""; // 调试文件路径，在Java环境中可以为空
                    String traceFlag = "0"; // 默认值
                    String taskId = dataDate; // 任务ID使用数据日期
                    
                    log.info("调用存储过程 bmssp_VGOP_banalyse: debugFile={}, traceFlag={}, taskId={}", debugFile, traceFlag, taskId);
                    
                    // **关键修改：使用dbaccess命令行方式执行存储过程，确保与Shell脚本执行环境一致**
                    boolean storedProcSuccess = executeStoredProcedureViaDbAccess(debugFile, traceFlag, taskId);
                    
                    if (!storedProcSuccess) {
                        log.error("存储过程 bmssp_VGOP_banalyse 执行失败");
                        return new ExportResult(interfaceId, false, "存储过程执行失败");
                    }
                    
                    log.info("存储过程 bmssp_VGOP_banalyse 执行完成，继续执行数据导出");
                    
                } catch (Exception e) {
                    log.error("调用业务分析存储过程失败: interfaceId={}, dataDate={}", interfaceId, dataDate, e);
                    return new ExportResult(interfaceId, false, "存储过程调用失败: " + e.getMessage());
                }
            }
            
            // 替换SQL模板中的参数
            String sql = exportConfig.getSqlTemplate()
                    .replace("{dataDate}", dataDate)
                    .replace("{revision}", String.valueOf(revision));
            
            // 处理日期范围占位符（用于日常任务）
            if (taskConfig.getTaskType().equals("daily")) {
                // 对于日常任务，计算前一天到当前数据日期的时间范围
                LocalDate currentDate = LocalDate.parse(dataDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
                String previousDay = currentDate.minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                String startTime = previousDay + "000000";
                String endTime = dataDate + "000000";
                
                sql = sql.replace("{starttime}", startTime)
                        .replace("{endtime}", endTime)
                        .replace("{previousDay}", previousDay);
            }
            // 统一日期处理逻辑：对于日统计任务，导出目录应使用前一天日期（与Shell脚本逻辑保持一致）
            String exportDirDate = dataDate;
            if (taskConfig.getTaskType().equals("daily")) {
                exportDirDate = DateTimeUtil.calculateBeforeDay(dataDate);
                log.debug("日统计任务导出目录使用前一天日期: {} -> {}", dataDate, exportDirDate);
            }
            // 处理文件名模板中的占位符
            String tempFileName = exportConfig.getTempFileNameTemplate()
                    .replace("{dataDate}", exportDirDate)
                    .replace("{interfaceId}", interfaceId);
            
            // 如果是日常任务，还需要处理previousDay占位符
            if (taskConfig.getTaskType().equals("daily")) {
                tempFileName = tempFileName.replace("{previousDay}", exportDirDate);
            }
            
            log.debug("构建UNLOAD SQL: {}", sql);
            
            // 获取导出路径
            String exportRoot = vgopProperties.getExportPath();
            String cycleType = taskConfig.getTaskType();
            
            String exportPath = String.format("%s/%s/%s/", exportRoot, exportDirDate, cycleType);
            
            // 确保目录存在
            File exportDir = new File(exportPath);
            if (!exportDir.exists()) {
                boolean created = exportDir.mkdirs();
                log.debug("创建导出目录: {}, 结果: {}", exportPath, created);
            }
            
            String tempFilePath = exportPath + tempFileName;
            
            log.debug("临时文件路径: {}", tempFilePath);
            
            // 使用UNLOAD命令直接导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, tempFilePath, delimiter);
            
            if (success) {
                // 统计文件行数作为记录数
                File outputFile = new File(tempFilePath);
                long recordCount = countFileLines(outputFile);
                
                log.info("接口数据导出成功: interfaceId={}, 记录数={}", interfaceId, recordCount);
                return new ExportResult(interfaceId, true, 1, (int) recordCount);
            } else {
                return new ExportResult(interfaceId, false, "UNLOAD执行失败");
            }
            
        } catch (Exception e) {
            log.error("接口数据导出失败: interfaceId=" + interfaceId, e);
            return new ExportResult(interfaceId, false, e.getMessage());
        } finally {
            // 清理MDC上下文，避免内存泄漏
            MdcUtil.clearInterfaceContext();
            MdcUtil.clearOperationType();
        }
    }
    

    /**
     * 统计文件行数
     */
    private long countFileLines(File file) {
        if (!file.exists()) {
            return 0;
        }
        
        try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(file))) {
            long lines = 0;
            while (reader.readLine() != null) {
                lines++;
            }
            return lines;
        } catch (Exception e) {
            log.warn("统计文件行数失败: {}", file.getAbsolutePath(), e);
            return 0;
        }
    }
    
    /**
     * 数据导出结果类
     */
    public static class ExportResult {
        private String interfaceId;
        private boolean success;
        private int fileCount;
        private int totalRows;
        private String errorMessage;
        
        public ExportResult() {
        }
        
        public ExportResult(String interfaceId, boolean success, int fileCount, int totalRows) {
            this.interfaceId = interfaceId;
            this.success = success;
            this.fileCount = fileCount;
            this.totalRows = totalRows;
        }
        
        public ExportResult(String interfaceId, boolean success, String errorMessage) {
            this.interfaceId = interfaceId;
            this.success = success;
            this.errorMessage = errorMessage;
            this.fileCount = 0;
            this.totalRows = 0;
        }
        
        public String getInterfaceId() {
            return interfaceId;
        }
        
        public void setInterfaceId(String interfaceId) {
            this.interfaceId = interfaceId;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public int getFileCount() {
            return fileCount;
        }
        
        public void setFileCount(int fileCount) {
            this.fileCount = fileCount;
        }
        
        public int getTotalRows() {
            return totalRows;
        }
        
        public void setTotalRows(int totalRows) {
            this.totalRows = totalRows;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }

    /**
     * 通过dbaccess命令行方式执行存储过程
     * 确保与原始Shell脚本的执行环境一致
     * 
     * @param debugFile 调试文件路径
     * @param traceFlag 跟踪标志
     * @param taskId 任务ID
     * @return 执行是否成功
     */
    private boolean executeStoredProcedureViaDbAccess(String debugFile, String traceFlag, String taskId) {
        // MDC上下文已在调用方法中设置，这里的日志会自动包含interfaceId信息
        try {
            // 获取数据库名称
            String databaseName = databaseUtil.getDatabaseName();
            log.info("通过dbaccess执行存储过程，数据库: {}", databaseName);
            
            // 构建存储过程调用SQL，与原始脚本保持一致
            String sqlCommand = String.format(
                "set lock mode to wait 10;call bmssp_VGOP_banalyse(\"%s\",\"%s\",\"%s000000\");",
                debugFile, 0, taskId
            );
            
            log.info("存储过程SQL命令: {}", sqlCommand);
            
            // 构建dbaccess命令
            ProcessBuilder processBuilder = new ProcessBuilder("dbaccess", databaseName);
            processBuilder.redirectErrorStream(true);
            
            Process process = processBuilder.start();
            
            // 向进程输入SQL命令
            try (var outputStream = process.getOutputStream()) {
                outputStream.write(sqlCommand.getBytes());
                outputStream.flush();
            }
            
            // 读取进程输出
            StringBuilder output = new StringBuilder();
            try (var inputStream = process.getInputStream();
                 var reader = new java.io.BufferedReader(new java.io.InputStreamReader(inputStream))) {
                
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            // 等待进程完成
            int exitCode = process.waitFor();
            String processOutput = output.toString();
            
            log.info("dbaccess存储过程执行结果 - 退出码: {}, 输出: {}", exitCode, processOutput);
            
            if (exitCode == 0) {
                log.info("存储过程通过dbaccess执行成功");
                return true;
            } else {
                log.error("存储过程通过dbaccess执行失败，退出码: {}, 输出: {}", exitCode, processOutput);
                return false;
            }
            
        } catch (Exception e) {
            log.error("通过dbaccess执行存储过程时发生异常", e);
            return false;
        }
    }
    
} 